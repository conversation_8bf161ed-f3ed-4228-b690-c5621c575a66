import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize
from scipy.spatial.distance import pdist, squareform
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子确保结果可重现
np.random.seed(42)

# 定义复杂的多峰函数 - 模拟蛋白质折叠能量景观
def protein_energy_landscape(coords, n_residues=8):
    """
    模拟蛋白质折叠的能量景观函数
    coords: 形状为(n_residues, 2)的坐标数组
    """
    coords = coords.reshape(n_residues, 2)
    
    # 1. 键长约束能量 (相邻残基距离约束)
    bond_energy = 0
    target_bond_length = 1.0
    for i in range(n_residues - 1):
        dist = np.linalg.norm(coords[i+1] - coords[i])
        bond_energy += 100 * (dist - target_bond_length)**2
    
    # 2. 角度约束能量 (避免过度弯曲)
    angle_energy = 0
    for i in range(n_residues - 2):
        v1 = coords[i+1] - coords[i]
        v2 = coords[i+2] - coords[i+1]
        v1_norm = v1 / np.linalg.norm(v1)
        v2_norm = v2 / np.linalg.norm(v2)
        cos_angle = np.dot(v1_norm, v2_norm)
        # 惩罚过度弯曲 (cos_angle接近-1)
        angle_energy += 10 * (cos_angle + 0.3)**2
    
    # 3. 非键相互作用 (Lennard-Jones势)
    lj_energy = 0
    for i in range(n_residues):
        for j in range(i + 2, n_residues):  # 跳过相邻残基
            dist = np.linalg.norm(coords[i] - coords[j])
            if dist > 0.1:  # 避免除零
                # 简化的LJ势: 4*epsilon*[(sigma/r)^12 - (sigma/r)^6]
                sigma = 0.8
                epsilon = 2.0
                lj_energy += 4 * epsilon * ((sigma/dist)**12 - (sigma/dist)**6)
    
    # 4. 疏水相互作用 (特定残基对的吸引)
    hydrophobic_residues = [1, 3, 5, 7]  # 假设这些是疏水残基
    hydrophobic_energy = 0
    for i in hydrophobic_residues:
        for j in hydrophobic_residues:
            if i < j:
                dist = np.linalg.norm(coords[i] - coords[j])
                if dist < 2.0:
                    hydrophobic_energy -= 5.0 * np.exp(-dist**2)
    
    return bond_energy + angle_energy + lj_energy + hydrophobic_energy

# 初始化多个随机构象
n_residues = 8
n_conformations = 6

# 创建图形
fig, axes = plt.subplots(2, 3, figsize=(15, 10))
axes = axes.flatten()

conformations = []
energies = []
optimized_conformations = []
optimized_energies = []

for i in range(n_conformations):
    # 生成初始随机构象
    initial_coords = np.random.randn(n_residues, 2) * 2
    initial_coords[0] = [0, 0]  # 固定第一个残基
    
    # 计算初始能量
    initial_energy = protein_energy_landscape(initial_coords.flatten())
    
    # 优化构象
    result = minimize(
        protein_energy_landscape, 
        initial_coords.flatten(),
        method='L-BFGS-B',
        options={'maxiter': 1000}
    )
    
    optimized_coords = result.x.reshape(n_residues, 2)
    optimized_energy = result.fun
    
    conformations.append(initial_coords)
    energies.append(initial_energy)
    optimized_conformations.append(optimized_coords)
    optimized_energies.append(optimized_energy)
    
    # 绘制子图
    ax = axes[i]
    
    # 绘制初始构象 (红色虚线)
    ax.plot(initial_coords[:, 0], initial_coords[:, 1], 'r--', alpha=0.5, linewidth=2, label='Initial')
    ax.scatter(initial_coords[:, 0], initial_coords[:, 1], c='red', s=50, alpha=0.7, zorder=5)
    
    # 绘制优化后构象 (蓝色实线)
    ax.plot(optimized_coords[:, 0], optimized_coords[:, 1], 'b-', linewidth=3, label='Optimized')
    ax.scatter(optimized_coords[:, 0], optimized_coords[:, 1], c='blue', s=80, zorder=10)
    
    # 标记残基编号
    for j, (x, y) in enumerate(optimized_coords):
        ax.annotate(f'{j}', (x, y), xytext=(5, 5), textcoords='offset points', 
                   fontsize=8, fontweight='bold')
    
    # 高亮疏水残基
    hydrophobic_residues = [1, 3, 5, 7]
    for hr in hydrophobic_residues:
        circle = patches.Circle(optimized_coords[hr], 0.3, fill=False, 
                              edgecolor='orange', linewidth=2, linestyle='--')
        ax.add_patch(circle)
    
    ax.set_title(f'Conformation {i+1}\nE_init={initial_energy:.1f}, E_opt={optimized_energy:.1f}', 
                fontsize=10)
    ax.set_xlim(-4, 4)
    ax.set_ylim(-4, 4)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')
    
    if i == 0:
        ax.legend(fontsize=8)

plt.tight_layout()
plt.suptitle('Protein Folding Energy Landscape Optimization\n' + 
             'Red dashed: Initial random conformations\n' +
             'Blue solid: Energy-minimized conformations\n' +
             'Orange circles: Hydrophobic residues (1,3,5,7)', 
             fontsize=12, y=0.98)

# 添加能量统计信息
min_energy_idx = np.argmin(optimized_energies)
max_energy_idx = np.argmax(optimized_energies)

plt.figtext(0.02, 0.02, 
           f'Global minimum: Conformation {min_energy_idx+1} (E = {optimized_energies[min_energy_idx]:.1f})\n' +
           f'Highest energy: Conformation {max_energy_idx+1} (E = {optimized_energies[max_energy_idx]:.1f})\n' +
           f'Energy range: {max(optimized_energies) - min(optimized_energies):.1f}',
           fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))

plt.show()

# 打印详细结果用于分析
print("=== 蛋白质折叠优化结果分析 ===")
for i in range(n_conformations):
    print(f"构象 {i+1}: 初始能量 = {energies[i]:.2f}, 优化后能量 = {optimized_energies[i]:.2f}, 降低 = {energies[i] - optimized_energies[i]:.2f}")

print(f"\n全局最优构象: {min_energy_idx+1} (能量 = {optimized_energies[min_energy_idx]:.2f})")
print(f"能量最高构象: {max_energy_idx+1} (能量 = {optimized_energies[max_energy_idx]:.2f})")
