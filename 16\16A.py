# 题目16：卷积神经网络特征图可视化分析
# 图片内容：显示CNN不同层的特征图激活模式

import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import numpy as np
from torchvision import transforms
from PIL import Image

class SimpleCNN(nn.Module):
    def __init__(self):
        super(SimpleCNN, self).__init__()
        self.conv1 = nn.Conv2d(1, 16, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(16, 32, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.pool = nn.MaxPool2d(2, 2)
        
    def forward(self, x):
        # 第一层卷积+激活+池化
        x1 = F.relu(self.conv1(x))
        x1_pooled = self.pool(x1)
        
        # 第二层卷积+激活+池化
        x2 = <PERSON>.relu(self.conv2(x1_pooled))
        x2_pooled = self.pool(x2)
        
        # 第三层卷积+激活+池化
        x3 = F.relu(self.conv3(x2_pooled))
        x3_pooled = self.pool(x3)
        
        return x1, x2, x3

# 创建输入数据（手写数字"8"的简化版本）
def create_digit_8():
    img = np.zeros((28, 28))
    # 上圆
    for i in range(6, 14):
        for j in range(8, 20):
            if ((i-9)**2 + (j-14)**2) <= 25 and ((i-9)**2 + (j-14)**2) >= 16:
                img[i, j] = 1.0
    # 下圆
    for i in range(14, 22):
        for j in range(8, 20):
            if ((i-18)**2 + (j-14)**2) <= 25 and ((i-18)**2 + (j-14)**2) >= 16:
                img[i, j] = 1.0
    # 连接部分
    img[12:16, 12:16] = 1.0
    return img

# 初始化模型和数据
model = SimpleCNN()
input_img = create_digit_8()
input_tensor = torch.FloatTensor(input_img).unsqueeze(0).unsqueeze(0)

# 前向传播获取特征图
with torch.no_grad():
    feat1, feat2, feat3 = model(input_tensor)

# 可视化特征图
fig, axes = plt.subplots(3, 6, figsize=(15, 8))

# 显示原始图像
axes[0, 0].imshow(input_img, cmap='gray')
axes[0, 0].set_title('Original')
axes[0, 0].axis('off')

# 第一层特征图（选择前5个通道）
for i in range(5):
    axes[0, i+1].imshow(feat1[0, i].numpy(), cmap='viridis')
    axes[0, i+1].set_title(f'Conv1-{i+1}')
    axes[0, i+1].axis('off')

# 第二层特征图（选择前6个通道）
for i in range(6):
    axes[1, i].imshow(feat2[0, i].numpy(), cmap='plasma')
    axes[1, i].set_title(f'Conv2-{i+1}')
    axes[1, i].axis('off')

# 第三层特征图（选择前6个通道）
for i in range(6):
    axes[2, i].imshow(feat3[0, i].numpy(), cmap='inferno')
    axes[2, i].set_title(f'Conv3-{i+1}')
    axes[2, i].axis('off')

plt.tight_layout()
plt.suptitle('CNN Feature Maps Visualization', y=1.02, fontsize=16)
plt.show()

# 计算每层的激活统计信息
layer1_mean = torch.mean(feat1).item()
layer2_mean = torch.mean(feat2).item()
layer3_mean = torch.mean(feat3).item()

layer1_std = torch.std(feat1).item()
layer2_std = torch.std(feat2).item()
layer3_std = torch.std(feat3).item()

print(f"Layer 1 - Mean: {layer1_mean:.4f}, Std: {layer1_std:.4f}")
print(f"Layer 2 - Mean: {layer2_mean:.4f}, Std: {layer2_std:.4f}")
print(f"Layer 3 - Mean: {layer3_mean:.4f}, Std: {layer3_std:.4f}")

# 计算感受野大小
receptive_field_layer1 = 3  # 3x3 kernel
receptive_field_layer2 = 3 + 2*2  # 3 + 2*(kernel_size-1) after pooling
receptive_field_layer3 = 7 + 4*2  # 7 + 4*(kernel_size-1) after another pooling

print(f"Receptive field sizes: Layer1={receptive_field_layer1}, Layer2={receptive_field_layer2}, Layer3={receptive_field_layer3}")
