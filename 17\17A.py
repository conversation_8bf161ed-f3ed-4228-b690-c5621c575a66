# 题目17：Transformer注意力机制热力图分析
# 图片内容：显示多头注意力的权重分布热力图

import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

class MultiHeadAttention(nn.Module):
    def __init__(self, d_model=512, num_heads=8):
        super(MultiHeadAttention, self).__init__()
        self.num_heads = num_heads
        self.d_model = d_model
        self.d_k = d_model // num_heads
        
        self.W_q = nn.Linear(d_model, d_model, bias=False)
        self.W_k = nn.Linear(d_model, d_model, bias=False)
        self.W_v = nn.Linear(d_model, d_model, bias=False)
        self.W_o = nn.Linear(d_model, d_model)
        
    def forward(self, query, key, value, mask=None):
        batch_size, seq_len, d_model = query.size()
        
        # 线性变换并重塑为多头
        Q = self.W_q(query).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K = self.W_k(key).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        V = self.W_v(value).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # 应用softmax
        attention_weights = torch.softmax(scores, dim=-1)
        
        # 应用注意力权重
        context = torch.matmul(attention_weights, V)
        
        # 重塑并通过输出层
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, d_model)
        output = self.W_o(context)
        
        return output, attention_weights

# 创建示例句子的词嵌入（模拟）
sentence = ["The", "cat", "sat", "on", "the", "mat", "and", "looked", "around"]
seq_len = len(sentence)
d_model = 512

# 初始化模型
attention = MultiHeadAttention(d_model=d_model, num_heads=8)

# 创建输入（随机初始化，但设置种子保证可重现）
torch.manual_seed(42)
input_embeddings = torch.randn(1, seq_len, d_model)

# 前向传播
with torch.no_grad():
    output, attention_weights = attention(input_embeddings, input_embeddings, input_embeddings)

# 可视化注意力权重
fig, axes = plt.subplots(2, 4, figsize=(16, 8))
axes = axes.flatten()

for head in range(8):
    # 获取当前头的注意力权重
    head_attention = attention_weights[0, head].numpy()
    
    # 创建热力图
    sns.heatmap(head_attention, 
                xticklabels=sentence, 
                yticklabels=sentence,
                cmap='Blues',
                ax=axes[head],
                cbar=True,
                square=True)
    
    axes[head].set_title(f'Head {head+1}')
    axes[head].set_xlabel('Key Position')
    axes[head].set_ylabel('Query Position')

plt.tight_layout()
plt.suptitle('Multi-Head Attention Weights Visualization', y=1.02, fontsize=16)
plt.show()

# 分析注意力模式
def analyze_attention_patterns(attention_weights, sentence):
    batch_size, num_heads, seq_len, _ = attention_weights.shape
    
    # 计算每个头的注意力分布熵
    entropies = []
    for head in range(num_heads):
        head_weights = attention_weights[0, head]
        # 计算每行的熵（每个query位置的注意力分布熵）
        row_entropies = []
        for i in range(seq_len):
            p = head_weights[i] + 1e-8  # 避免log(0)
            entropy = -torch.sum(p * torch.log(p)).item()
            row_entropies.append(entropy)
        entropies.append(np.mean(row_entropies))
    
    # 找到最集中和最分散的注意力头
    most_focused_head = np.argmin(entropies)
    most_distributed_head = np.argmax(entropies)
    
    print(f"Most focused attention head: {most_focused_head + 1} (entropy: {entropies[most_focused_head]:.3f})")
    print(f"Most distributed attention head: {most_distributed_head + 1} (entropy: {entropies[most_distributed_head]:.3f})")
    
    # 分析自注意力强度
    self_attention_scores = []
    for head in range(num_heads):
        self_attn = torch.diag(attention_weights[0, head]).mean().item()
        self_attention_scores.append(self_attn)
    
    avg_self_attention = np.mean(self_attention_scores)
    print(f"Average self-attention score: {avg_self_attention:.3f}")
    
    return entropies, most_focused_head, most_distributed_head

# 执行分析
entropies, focused_head, distributed_head = analyze_attention_patterns(attention_weights, sentence)

# 计算注意力权重的统计信息
attention_stats = {
    'mean': attention_weights.mean().item(),
    'std': attention_weights.std().item(),
    'max': attention_weights.max().item(),
    'min': attention_weights.min().item()
}

print(f"Attention weights statistics: {attention_stats}")
