import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import find_peaks, hilbert
from scipy.fft import fft, fftfreq
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
np.random.seed(123)

# 时间参数
fs = 1000  # 采样频率
T = 2.0    # 总时间
t = np.linspace(0, T, int(fs * T), endpoint=False)

# 定义复杂的非线性振荡系统 - 模拟心电图信号
def generate_ecg_like_signal(t, heart_rate=72, noise_level=0.1):
    """
    生成类似心电图的复杂非线性信号
    包含P波、QRS复合波、T波的特征
    """
    # 基础心率周期
    period = 60.0 / heart_rate  # 秒
    
    # 初始化信号
    signal = np.zeros_like(t)
    
    for beat_start in np.arange(0, T, period):
        # 为每个心跳周期添加波形成分
        beat_mask = (t >= beat_start) & (t < beat_start + period)
        if not np.any(beat_mask):
            continue
            
        t_beat = t[beat_mask] - beat_start
        beat_signal = np.zeros_like(t_beat)
        
        # P波 (心房去极化)
        p_center = 0.08
        p_width = 0.04
        p_amplitude = 0.15
        p_wave = p_amplitude * np.exp(-((t_beat - p_center) / p_width)**2)
        
        # QRS复合波 (心室去极化) - 复杂的多峰结构
        qrs_center = 0.18
        # Q波 (负向)
        q_wave = -0.1 * np.exp(-((t_beat - (qrs_center - 0.02)) / 0.01)**2)
        # R波 (主峰，正向)
        r_wave = 1.0 * np.exp(-((t_beat - qrs_center) / 0.015)**2)
        # S波 (负向)
        s_wave = -0.3 * np.exp(-((t_beat - (qrs_center + 0.02)) / 0.015)**2)
        
        # T波 (心室复极化)
        t_center = 0.35
        t_width = 0.08
        t_amplitude = 0.25
        t_wave = t_amplitude * np.exp(-((t_beat - t_center) / t_width)**2)
        
        # 组合所有波形
        beat_signal = p_wave + q_wave + r_wave + s_wave + t_wave
        
        # 添加非线性调制 (模拟心率变异性)
        modulation = 1 + 0.1 * np.sin(2 * np.pi * 0.3 * (beat_start + t_beat))
        beat_signal *= modulation
        
        signal[beat_mask] += beat_signal
    
    # 添加基线漂移
    baseline_drift = 0.05 * np.sin(2 * np.pi * 0.1 * t) + 0.03 * np.sin(2 * np.pi * 0.05 * t)
    
    # 添加高频噪声
    noise = noise_level * np.random.randn(len(t))
    
    return signal + baseline_drift + noise

# 生成三种不同的信号
signals = {}
signal_params = {
    'Normal': {'heart_rate': 72, 'noise_level': 0.05},
    'Tachycardia': {'heart_rate': 120, 'noise_level': 0.08},
    'Arrhythmia': {'heart_rate': 85, 'noise_level': 0.12}
}

# 创建图形
fig = plt.figure(figsize=(16, 12))

# 生成信号并进行分析
for i, (condition, params) in enumerate(signal_params.items()):
    # 生成信号
    if condition == 'Arrhythmia':
        # 心律不齐：不规则的心率
        base_signal = generate_ecg_like_signal(t, **params)
        # 添加额外的不规则成分
        irregular_beats = np.zeros_like(t)
        for extra_beat in [0.3, 0.9, 1.5]:
            if extra_beat < T:
                beat_mask = (t >= extra_beat) & (t < extra_beat + 0.1)
                irregular_beats[beat_mask] += 0.4 * np.exp(-((t[beat_mask] - extra_beat) / 0.02)**2)
        signals[condition] = base_signal + irregular_beats
    else:
        signals[condition] = generate_ecg_like_signal(t, **params)

# 绘制时域信号
for i, (condition, signal) in enumerate(signals.items()):
    ax1 = plt.subplot(3, 4, i*4 + 1)
    plt.plot(t, signal, 'b-', linewidth=1.5)
    plt.title(f'{condition} - Time Domain')
    plt.xlabel('Time (s)')
    plt.ylabel('Amplitude (mV)')
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 2)
    
    # 标记R峰
    peaks, _ = find_peaks(signal, height=0.3, distance=int(0.3*fs))
    if len(peaks) > 0:
        plt.plot(t[peaks], signal[peaks], 'ro', markersize=6)
        # 计算心率
        if len(peaks) > 1:
            rr_intervals = np.diff(t[peaks])
            avg_hr = 60 / np.mean(rr_intervals)
            plt.text(0.05, 0.95, f'HR: {avg_hr:.0f} bpm', transform=ax1.transAxes, 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))

# 绘制频域分析
for i, (condition, signal) in enumerate(signals.items()):
    ax2 = plt.subplot(3, 4, i*4 + 2)
    
    # FFT分析
    fft_signal = fft(signal)
    freqs = fftfreq(len(signal), 1/fs)
    magnitude = np.abs(fft_signal)
    
    # 只显示正频率部分
    pos_mask = freqs >= 0
    plt.semilogy(freqs[pos_mask], magnitude[pos_mask], 'g-', linewidth=1.5)
    plt.title(f'{condition} - Frequency Domain')
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Magnitude')
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 50)
    
    # 标记主要频率成分
    main_peaks, _ = find_peaks(magnitude[pos_mask], height=np.max(magnitude)*0.1)
    if len(main_peaks) > 0:
        main_freqs = freqs[pos_mask][main_peaks]
        main_mags = magnitude[pos_mask][main_peaks]
        plt.plot(main_freqs, main_mags, 'ro', markersize=6)

# 绘制希尔伯特变换分析 (瞬时幅度和相位)
for i, (condition, signal) in enumerate(signals.items()):
    ax3 = plt.subplot(3, 4, i*4 + 3)
    
    # 希尔伯特变换
    analytic_signal = hilbert(signal)
    instantaneous_amplitude = np.abs(analytic_signal)
    instantaneous_phase = np.angle(analytic_signal)
    
    # 绘制瞬时幅度
    plt.plot(t, instantaneous_amplitude, 'r-', linewidth=2, label='Inst. Amplitude')
    plt.plot(t, signal, 'b-', alpha=0.5, linewidth=1, label='Original')
    plt.title(f'{condition} - Hilbert Analysis')
    plt.xlabel('Time (s)')
    plt.ylabel('Amplitude')
    plt.legend(fontsize=8)
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 2)

# 绘制相位空间重构 (延迟嵌入)
for i, (condition, signal) in enumerate(signals.items()):
    ax4 = plt.subplot(3, 4, i*4 + 4)
    
    # 延迟嵌入重构相空间
    delay = 10  # 延迟样本数
    if len(signal) > delay:
        x = signal[:-delay]
        y = signal[delay:]
        
        # 创建密度图
        plt.hexbin(x, y, gridsize=30, cmap='Blues', alpha=0.8)
        plt.colorbar(label='Density')
        plt.title(f'{condition} - Phase Space')
        plt.xlabel('x(t)')
        plt.ylabel('x(t+τ)')
        plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.suptitle('Nonlinear ECG Signal Analysis: Time-Frequency-Phase Space Decomposition\n' +
             'Red dots: R-peaks (time domain) and dominant frequencies (frequency domain)', 
             fontsize=14, y=0.98)

plt.show()

# 计算并打印关键指标
print("=== 心电信号非线性分析结果 ===")
for condition, signal in signals.items():
    # R峰检测和心率变异性
    peaks, _ = find_peaks(signal, height=0.3, distance=int(0.3*fs))
    if len(peaks) > 1:
        rr_intervals = np.diff(t[peaks]) * 1000  # 转换为毫秒
        hrv_rmssd = np.sqrt(np.mean(np.diff(rr_intervals)**2))  # HRV指标
        avg_hr = 60000 / np.mean(rr_intervals)
        
        print(f"{condition}:")
        print(f"  平均心率: {avg_hr:.1f} bpm")
        print(f"  HRV (RMSSD): {hrv_rmssd:.2f} ms")
        print(f"  R峰数量: {len(peaks)}")
        
        # 频域主导频率
        fft_signal = fft(signal)
        freqs = fftfreq(len(signal), 1/fs)
        magnitude = np.abs(fft_signal)
        pos_mask = freqs >= 0
        dominant_freq_idx = np.argmax(magnitude[pos_mask])
        dominant_freq = freqs[pos_mask][dominant_freq_idx]
        print(f"  主导频率: {dominant_freq:.2f} Hz")
        print()
