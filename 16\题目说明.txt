题目16：卷积神经网络特征图可视化分析

【图片内容】：显示CNN三层卷积的特征图激活模式，包括原始输入图像（手写数字"8"）和各层的特征图可视化

【问题】：
观察图片中的特征图激活模式，根据代码输出的统计信息和可视化结果，以下关于Conv1-2和Conv2-3特征图激活强度对比的描述哪个是正确的？

A. Conv1-2特征图在数字"8"的上半圆区域激活最强，而Conv2-3特征图在下半圆区域激活更明显
B. Conv1-2和Conv2-3特征图的激活模式完全相同，没有任何差异
C. Conv1-2特征图显示均匀的激活分布，Conv2-3特征图只在图像中心有激活
D. Conv1-2特征图在数字"8"的连接部分(中间区域)激活最强，Conv2-3特征图对整体轮廓响应更强

【答案】：D

【推理过程】：
1）由于代码中使用了确定的数字"8"生成函数create_digit_8()，特征图的激活模式是固定的，必须通过观察图片中的具体激活区域来判断；
2）从图片中可以观察到Conv1-2特征图在数字"8"的中间连接部分(代码中的img[12:16, 12:16] = 1.0区域)显示出最强的激活响应；
3）Conv2-3特征图作为更深层的特征，对数字"8"的整体轮廓结构有更强的响应，这与卷积层逐渐学习更抽象特征的理论一致。
