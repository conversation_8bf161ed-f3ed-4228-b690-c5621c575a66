题目16：卷积神经网络特征图可视化分析

【图片内容】：显示CNN三层卷积的特征图激活模式，包括原始输入图像（手写数字"8"）和各层的特征图可视化

【问题】：
观察图片中CNN各层特征图的激活模式，根据深度学习理论和代码实现，以下关于特征图演化规律的描述哪个是正确的？

A. 第一层主要检测边缘和简单纹理，第二层开始组合形成更复杂的模式，第三层的感受野大小为7×7
B. 第一层主要检测边缘和简单纹理，第二层开始组合形成更复杂的模式，第三层的感受野大小为15×15  
C. 各层特征图的激活强度随深度增加而线性递减，第三层感受野大小为11×11
D. 浅层特征图主要响应全局特征，深层特征图主要响应局部细节，第三层感受野大小为9×9

【答案】：B

【推理过程】：
1）根据CNN的层次化特征学习理论，第一层卷积主要检测边缘、线条等低级特征，第二层开始组合这些基础特征形成更复杂的模式；
2）感受野计算：第一层3×3卷积的感受野为3，经过2×2池化后，第二层的感受野为3+2×2=7，再经过池化后，第三层的感受野为7+4×2=15；
3）从图片可以观察到，浅层特征图显示更多局部细节激活，深层特征图显示更抽象的模式响应，符合CNN的特征层次化学习规律。
