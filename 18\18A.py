# 题目18：GAN训练过程损失函数动态分析
# 图片内容：显示生成器和判别器损失函数在训练过程中的动态变化

import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
import numpy as np
from torch.distributions import Normal

class Generator(nn.Module):
    def __init__(self, noise_dim=100, output_dim=784):
        super(Generator, self).__init__()
        self.model = nn.Sequential(
            nn.Linear(noise_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 512),
            nn.ReLU(),
            nn.Linear(512, output_dim),
            nn.Tanh()
        )
    
    def forward(self, x):
        return self.model(x)

class Discriminator(nn.Module):
    def __init__(self, input_dim=784):
        super(Discriminator, self).__init__()
        self.model = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.<PERSON><PERSON><PERSON>e<PERSON><PERSON>(0.2),
            nn.Dropout(0.3),
            nn.<PERSON>ar(512, 256),
            nn.<PERSON><PERSON><PERSON>(0.2),
            nn.Dropout(0.3),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        return self.model(x)

# 初始化模型
torch.manual_seed(42)
generator = Generator()
discriminator = Discriminator()

# 优化器
g_optimizer = optim.Adam(generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
d_optimizer = optim.Adam(discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))

# 损失函数
criterion = nn.BCELoss()

# 模拟真实数据（简化的2D高斯分布）
def generate_real_data(batch_size=64):
    # 创建两个高斯分布的混合
    data1 = torch.randn(batch_size//2, 784) * 0.5 + 1.0
    data2 = torch.randn(batch_size//2, 784) * 0.5 - 1.0
    return torch.cat([data1, data2], dim=0)

# 训练循环
num_epochs = 200
batch_size = 64
noise_dim = 100

g_losses = []
d_losses = []
d_real_acc = []
d_fake_acc = []

for epoch in range(num_epochs):
    # 训练判别器
    discriminator.zero_grad()
    
    # 真实数据
    real_data = generate_real_data(batch_size)
    real_labels = torch.ones(batch_size, 1)
    real_output = discriminator(real_data)
    d_real_loss = criterion(real_output, real_labels)
    
    # 生成假数据
    noise = torch.randn(batch_size, noise_dim)
    fake_data = generator(noise)
    fake_labels = torch.zeros(batch_size, 1)
    fake_output = discriminator(fake_data.detach())
    d_fake_loss = criterion(fake_output, fake_labels)
    
    # 判别器总损失
    d_loss = d_real_loss + d_fake_loss
    d_loss.backward()
    d_optimizer.step()
    
    # 训练生成器
    generator.zero_grad()
    
    # 生成器希望判别器认为假数据是真的
    noise = torch.randn(batch_size, noise_dim)
    fake_data = generator(noise)
    fake_output = discriminator(fake_data)
    g_loss = criterion(fake_output, real_labels)  # 希望输出接近1
    
    g_loss.backward()
    g_optimizer.step()
    
    # 记录损失和准确率
    g_losses.append(g_loss.item())
    d_losses.append(d_loss.item())
    
    # 计算判别器准确率
    d_real_accuracy = (real_output > 0.5).float().mean().item()
    d_fake_accuracy = (fake_output < 0.5).float().mean().item()
    d_real_acc.append(d_real_accuracy)
    d_fake_acc.append(d_fake_accuracy)

# 可视化训练过程
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

# 损失函数曲线
epochs_range = range(1, num_epochs + 1)
ax1.plot(epochs_range, g_losses, label='Generator Loss', color='blue', alpha=0.7)
ax1.plot(epochs_range, d_losses, label='Discriminator Loss', color='red', alpha=0.7)
ax1.set_xlabel('Epoch')
ax1.set_ylabel('Loss')
ax1.set_title('Generator vs Discriminator Loss')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 判别器准确率
ax2.plot(epochs_range, d_real_acc, label='Real Data Accuracy', color='green', alpha=0.7)
ax2.plot(epochs_range, d_fake_acc, label='Fake Data Accuracy', color='orange', alpha=0.7)
ax2.axhline(y=0.5, color='black', linestyle='--', alpha=0.5, label='Random Guess')
ax2.set_xlabel('Epoch')
ax2.set_ylabel('Accuracy')
ax2.set_title('Discriminator Accuracy on Real vs Fake Data')
ax2.legend()
ax2.grid(True, alpha=0.3)

# 损失比值分析
loss_ratio = np.array(g_losses) / (np.array(d_losses) + 1e-8)
ax3.plot(epochs_range, loss_ratio, color='purple', alpha=0.7)
ax3.axhline(y=1.0, color='black', linestyle='--', alpha=0.5, label='Equal Loss')
ax3.set_xlabel('Epoch')
ax3.set_ylabel('G_Loss / D_Loss')
ax3.set_title('Generator to Discriminator Loss Ratio')
ax3.legend()
ax3.grid(True, alpha=0.3)

# 训练稳定性分析（损失的滑动平均）
window_size = 10
g_losses_smooth = np.convolve(g_losses, np.ones(window_size)/window_size, mode='valid')
d_losses_smooth = np.convolve(d_losses, np.ones(window_size)/window_size, mode='valid')
smooth_epochs = range(window_size, num_epochs + 1)

ax4.plot(smooth_epochs, g_losses_smooth, label='Generator (Smoothed)', color='blue', linewidth=2)
ax4.plot(smooth_epochs, d_losses_smooth, label='Discriminator (Smoothed)', color='red', linewidth=2)
ax4.set_xlabel('Epoch')
ax4.set_ylabel('Smoothed Loss')
ax4.set_title('Training Stability Analysis')
ax4.legend()
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 分析训练状态
final_g_loss = np.mean(g_losses[-20:])
final_d_loss = np.mean(d_losses[-20:])
final_d_real_acc = np.mean(d_real_acc[-20:])
final_d_fake_acc = np.mean(d_fake_acc[-20:])

print(f"Final 20 epochs average:")
print(f"Generator Loss: {final_g_loss:.4f}")
print(f"Discriminator Loss: {final_d_loss:.4f}")
print(f"Discriminator Real Accuracy: {final_d_real_acc:.4f}")
print(f"Discriminator Fake Accuracy: {final_d_fake_acc:.4f}")

# 判断训练状态
if final_d_real_acc > 0.8 and final_d_fake_acc > 0.8:
    print("Training Status: Discriminator is too strong")
elif final_d_real_acc < 0.6 and final_d_fake_acc < 0.6:
    print("Training Status: Generator is too strong")
else:
    print("Training Status: Relatively balanced")
