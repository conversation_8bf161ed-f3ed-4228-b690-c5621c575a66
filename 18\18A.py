import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.optimize import minimize_scalar
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
np.random.seed(456)

# 定义复杂的流体动力学系统 - 双摆混沌系统
def double_pendulum_system(t, y, L1=1.0, L2=1.0, m1=1.0, m2=1.0, g=9.81, damping=0.1):
    """
    双摆系统的微分方程
    y = [theta1, z1, theta2, z2] 其中 z1 = dtheta1/dt, z2 = dtheta2/dt
    """
    theta1, z1, theta2, z2 = y
    
    # 计算中间变量
    delta_theta = theta2 - theta1
    den1 = (m1 + m2) * L1 - m2 * L1 * np.cos(delta_theta) * np.cos(delta_theta)
    den2 = (L2 / L1) * den1
    
    # 计算角加速度
    num1 = (-m2 * L1 * z1**2 * np.sin(delta_theta) * np.cos(delta_theta) +
            m2 * g * np.sin(theta2) * np.cos(delta_theta) +
            m2 * L2 * z2**2 * np.sin(delta_theta) -
            (m1 + m2) * g * np.sin(theta1) -
            damping * z1)
    
    num2 = (-m2 * L2 * z2**2 * np.sin(delta_theta) * np.cos(delta_theta) +
            (m1 + m2) * g * np.sin(theta1) * np.cos(delta_theta) -
            (m1 + m2) * L1 * z1**2 * np.sin(delta_theta) -
            (m1 + m2) * g * np.sin(theta2) -
            damping * z2)
    
    dtheta1_dt = z1
    dz1_dt = num1 / den1
    dtheta2_dt = z2
    dz2_dt = num2 / den2
    
    return [dtheta1_dt, dz1_dt, dtheta2_dt, dz2_dt]

# 计算系统能量
def calculate_energy(y, L1=1.0, L2=1.0, m1=1.0, m2=1.0, g=9.81):
    """计算双摆系统的总能量"""
    theta1, z1, theta2, z2 = y
    
    # 动能
    T1 = 0.5 * m1 * (L1 * z1)**2
    T2 = 0.5 * m2 * ((L1 * z1)**2 + (L2 * z2)**2 + 2 * L1 * L2 * z1 * z2 * np.cos(theta1 - theta2))
    kinetic_energy = T1 + T2
    
    # 势能 (以最低点为零势能面)
    potential_energy = -(m1 + m2) * g * L1 * np.cos(theta1) - m2 * g * L2 * np.cos(theta2)
    
    return kinetic_energy + potential_energy

# 设置不同的初始条件
initial_conditions = [
    [np.pi/2, 0, np.pi/2, 0],      # 对称初始条件
    [np.pi/3, 0, 2*np.pi/3, 0],    # 不对称初始条件
    [np.pi/4, 1, 3*np.pi/4, -1],   # 有初始角速度
    [np.pi/6, 2, 5*np.pi/6, -2]    # 高初始角速度
]

# 时间参数
t_span = (0, 20)
t_eval = np.linspace(0, 20, 2000)

# 创建图形
fig = plt.figure(figsize=(16, 12))

# 颜色映射
colors = ['red', 'blue', 'green', 'orange']
condition_names = ['Symmetric', 'Asymmetric', 'Low Energy', 'High Energy']

# 存储解和能量
solutions = []
energies = []

# 求解每个初始条件
for i, y0 in enumerate(initial_conditions):
    sol = solve_ivp(double_pendulum_system, t_span, y0, t_eval=t_eval, 
                   method='RK45', rtol=1e-8, atol=1e-10)
    solutions.append(sol)
    
    # 计算能量随时间的变化
    energy_time = []
    for j in range(len(sol.t)):
        energy_time.append(calculate_energy(sol.y[:, j]))
    energies.append(energy_time)

# 1. 时间序列图
ax1 = plt.subplot(2, 3, 1)
for i, (sol, name, color) in enumerate(zip(solutions, condition_names, colors)):
    plt.plot(sol.t, sol.y[0], color=color, linewidth=2, label=f'{name} θ₁', alpha=0.8)
    plt.plot(sol.t, sol.y[2], color=color, linewidth=1, linestyle='--', label=f'{name} θ₂', alpha=0.6)

plt.xlabel('Time (s)')
plt.ylabel('Angle (rad)')
plt.title('Angular Displacement vs Time')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
plt.grid(True, alpha=0.3)

# 2. 相空间图 (θ₁ vs dθ₁/dt)
ax2 = plt.subplot(2, 3, 2)
for i, (sol, name, color) in enumerate(zip(solutions, condition_names, colors)):
    plt.plot(sol.y[0], sol.y[1], color=color, linewidth=1.5, label=name, alpha=0.8)
    # 标记起始点
    plt.plot(sol.y[0][0], sol.y[1][0], 'o', color=color, markersize=8, markeredgecolor='black')

plt.xlabel('θ₁ (rad)')
plt.ylabel('dθ₁/dt (rad/s)')
plt.title('Phase Space: Pendulum 1')
plt.legend(fontsize=8)
plt.grid(True, alpha=0.3)

# 3. 相空间图 (θ₂ vs dθ₂/dt)
ax3 = plt.subplot(2, 3, 3)
for i, (sol, name, color) in enumerate(zip(solutions, condition_names, colors)):
    plt.plot(sol.y[2], sol.y[3], color=color, linewidth=1.5, label=name, alpha=0.8)
    # 标记起始点
    plt.plot(sol.y[2][0], sol.y[3][0], 'o', color=color, markersize=8, markeredgecolor='black')

plt.xlabel('θ₂ (rad)')
plt.ylabel('dθ₂/dt (rad/s)')
plt.title('Phase Space: Pendulum 2')
plt.legend(fontsize=8)
plt.grid(True, alpha=0.3)

# 4. 能量守恒分析
ax4 = plt.subplot(2, 3, 4)
for i, (sol, energy, name, color) in enumerate(zip(solutions, energies, condition_names, colors)):
    plt.plot(sol.t, energy, color=color, linewidth=2, label=name)
    
    # 计算能量变化
    energy_change = (energy[-1] - energy[0]) / energy[0] * 100
    plt.text(0.02, 0.98 - i*0.05, f'{name}: {energy_change:+.2f}%', 
             transform=ax4.transAxes, color=color, fontweight='bold')

plt.xlabel('Time (s)')
plt.ylabel('Total Energy (J)')
plt.title('Energy Conservation Analysis')
plt.legend(fontsize=8)
plt.grid(True, alpha=0.3)

# 5. 庞加莱截面 (θ₁ = 0 时的 θ₂ vs dθ₂/dt)
ax5 = plt.subplot(2, 3, 5)
for i, (sol, name, color) in enumerate(zip(solutions, condition_names, colors)):
    # 找到 θ₁ 接近 0 的点 (上升穿越)
    theta1 = sol.y[0]
    dtheta1 = sol.y[1]
    theta2 = sol.y[2]
    dtheta2 = sol.y[3]
    
    # 寻找穿越点
    crossings_idx = []
    for j in range(1, len(theta1)):
        if theta1[j-1] < 0 and theta1[j] >= 0 and dtheta1[j] > 0:  # 上升穿越
            crossings_idx.append(j)
    
    if len(crossings_idx) > 0:
        crossing_theta2 = theta2[crossings_idx]
        crossing_dtheta2 = dtheta2[crossings_idx]
        plt.scatter(crossing_theta2, crossing_dtheta2, c=color, s=30, 
                   alpha=0.7, label=name, edgecolors='black', linewidth=0.5)

plt.xlabel('θ₂ (rad)')
plt.ylabel('dθ₂/dt (rad/s)')
plt.title('Poincaré Section (θ₁ = 0, dθ₁/dt > 0)')
plt.legend(fontsize=8)
plt.grid(True, alpha=0.3)

# 6. 轨迹在配置空间的投影
ax6 = plt.subplot(2, 3, 6)
for i, (sol, name, color) in enumerate(zip(solutions, condition_names, colors)):
    # 计算摆锤位置
    L1, L2 = 1.0, 1.0
    x1 = L1 * np.sin(sol.y[0])
    y1 = -L1 * np.cos(sol.y[0])
    x2 = x1 + L2 * np.sin(sol.y[2])
    y2 = y1 - L2 * np.cos(sol.y[2])
    
    # 绘制第二个摆锤的轨迹
    plt.plot(x2, y2, color=color, linewidth=1, alpha=0.6, label=name)
    
    # 标记起始和结束位置
    plt.plot(x2[0], y2[0], 'o', color=color, markersize=8, markeredgecolor='black')
    plt.plot(x2[-1], y2[-1], 's', color=color, markersize=6, markeredgecolor='black')

plt.xlabel('x₂ (m)')
plt.ylabel('y₂ (m)')
plt.title('Trajectory of Second Pendulum')
plt.legend(fontsize=8)
plt.grid(True, alpha=0.3)
plt.axis('equal')

plt.tight_layout()
plt.suptitle('Double Pendulum Chaotic Dynamics Analysis\n' +
             'Circles: Initial positions, Squares: Final positions', 
             fontsize=14, y=0.98)

plt.show()

# 计算李雅普诺夫指数的近似值
print("=== 双摆混沌动力学分析结果 ===")
for i, (sol, energy, name) in enumerate(zip(solutions, energies, condition_names)):
    print(f"{name} 初始条件:")
    print(f"  初始角度: θ₁={initial_conditions[i][0]:.3f}, θ₂={initial_conditions[i][2]:.3f}")
    print(f"  初始角速度: ω₁={initial_conditions[i][1]:.3f}, ω₂={initial_conditions[i][3]:.3f}")
    print(f"  初始能量: {energy[0]:.4f} J")
    print(f"  最终能量: {energy[-1]:.4f} J")
    print(f"  能量变化: {(energy[-1] - energy[0])/energy[0]*100:+.3f}%")
    
    # 计算轨道的复杂性指标 (近似)
    theta1_range = np.max(sol.y[0]) - np.min(sol.y[0])
    theta2_range = np.max(sol.y[2]) - np.min(sol.y[2])
    print(f"  θ₁ 变化范围: {theta1_range:.3f} rad")
    print(f"  θ₂ 变化范围: {theta2_range:.3f} rad")
    print()
