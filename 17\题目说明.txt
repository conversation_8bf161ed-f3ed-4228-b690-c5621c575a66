题目17：Transformer注意力机制热力图分析

【图片内容】：显示8个注意力头的权重分布热力图，每个热力图展示句子"The cat sat on the mat and looked around"中词与词之间的注意力权重

【问题】：
观察图片中多头注意力机制的热力图，根据Transformer架构原理和代码实现，关于注意力模式的以下分析哪个是正确的？

A. 所有注意力头都表现出相同的注意力模式，主要关注句子的开头和结尾词汇
B. 不同的注意力头学习到了不同的语言模式，有些头更关注局部依赖，有些头更关注长距离依赖，且自注意力分数通常较高
C. 注意力权重在所有位置上均匀分布，表明模型无法学习到有意义的语言结构
D. 只有第一个注意力头起作用，其他头的权重都接近于零

【答案】：B

【推理过程】：
1）根据Transformer的多头注意力机制设计，不同的注意力头被期望学习不同类型的语言关系，从热力图可以观察到各头确实呈现不同的注意力模式；
2）从代码中的自注意力分析可以看出，对角线上的自注意力分数（词对自身的注意力）通常较高，这符合语言模型中词汇自相关的特性；
3）热力图显示某些头更关注相邻词汇（局部依赖），而某些头在远距离词汇间也有较强连接（长距离依赖），体现了多头机制捕获不同语言现象的能力。
