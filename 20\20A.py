# 题目20：LSTM时间序列预测梯度流分析
# 图片内容：显示LSTM在处理长序列时的梯度流动和遗忘门激活模式

import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

class AnalyzableLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, num_layers=2, output_size=1):
        super(AnalyzableLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)
        
        # 用于存储中间状态
        self.hidden_states = []
        self.cell_states = []
        self.forget_gates = []
        
    def forward(self, x):
        batch_size = x.size(0)
        
        # 初始化隐藏状态和细胞状态
        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_size)
        c0 = torch.zeros(self.num_layers, batch_size, self.hidden_size)
        
        # 清空存储
        self.hidden_states = []
        self.cell_states = []
        self.forget_gates = []
        
        # 手动实现LSTM前向传播以获取门控信息
        h, c = h0[0], c0[0]  # 只分析第一层
        
        for t in range(x.size(1)):
            # 获取当前时间步的输入
            xt = x[:, t, :]
            
            # 计算门控值
            combined = torch.cat([xt, h], dim=1)
            
            # 使用LSTM的权重计算门控（简化版本）
            lstm_layer = self.lstm.weight_ih_l0, self.lstm.weight_hh_l0, self.lstm.bias_ih_l0, self.lstm.bias_hh_l0
            
            # 计算所有门控
            gates = torch.mm(combined, torch.cat([lstm_layer[0].T, lstm_layer[1].T], dim=0)) + torch.cat([lstm_layer[2], lstm_layer[3]])
            
            # 分离各个门控
            forget_gate = torch.sigmoid(gates[:, :self.hidden_size])
            input_gate = torch.sigmoid(gates[:, self.hidden_size:2*self.hidden_size])
            candidate = torch.tanh(gates[:, 2*self.hidden_size:3*self.hidden_size])
            output_gate = torch.sigmoid(gates[:, 3*self.hidden_size:])
            
            # 更新细胞状态和隐藏状态
            c = forget_gate * c + input_gate * candidate
            h = output_gate * torch.tanh(c)
            
            # 存储状态
            self.hidden_states.append(h.clone())
            self.cell_states.append(c.clone())
            self.forget_gates.append(forget_gate.clone())
        
        # 使用标准LSTM进行最终计算
        lstm_out, _ = self.lstm(x, (h0, c0))
        output = self.fc(lstm_out[:, -1, :])
        
        return output

# 生成复杂的时间序列数据
def generate_complex_sequence(seq_length=100, num_samples=1000):
    """生成包含长期依赖的复杂时间序列"""
    sequences = []
    targets = []
    
    for _ in range(num_samples):
        # 基础趋势
        t = np.linspace(0, 4*np.pi, seq_length)
        base_signal = np.sin(t) + 0.5 * np.sin(3*t)
        
        # 添加长期依赖：序列开始的值影响结束的值
        start_influence = np.random.randn() * 2
        long_term_effect = start_influence * np.exp(-t/20)  # 指数衰减的长期影响
        
        # 添加噪声
        noise = np.random.randn(seq_length) * 0.1
        
        # 组合信号
        sequence = base_signal + long_term_effect + noise
        
        # 目标：预测序列结束时的长期影响强度
        target = start_influence * np.exp(-4*np.pi/20)
        
        sequences.append(sequence)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

# 生成数据
seq_length = 50
X, y = generate_complex_sequence(seq_length=seq_length, num_samples=500)

# 转换为PyTorch张量
X_tensor = torch.FloatTensor(X).unsqueeze(-1)  # 添加特征维度
y_tensor = torch.FloatTensor(y).unsqueeze(-1)

# 初始化模型
model = AnalyzableLSTM(input_size=1, hidden_size=32, num_layers=1, output_size=1)
criterion = nn.MSELoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

# 训练模型
num_epochs = 100
losses = []

for epoch in range(num_epochs):
    model.train()
    optimizer.zero_grad()
    
    # 前向传播
    outputs = model(X_tensor)
    loss = criterion(outputs, y_tensor)
    
    # 反向传播
    loss.backward()
    optimizer.step()
    
    losses.append(loss.item())
    
    if (epoch + 1) % 20 == 0:
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {loss.item():.6f}')

# 分析模型行为
model.eval()
with torch.no_grad():
    # 选择一个样本进行分析
    sample_idx = 0
    sample_input = X_tensor[sample_idx:sample_idx+1]
    sample_output = model(sample_input)
    
    # 获取存储的状态
    hidden_states = torch.stack(model.hidden_states).squeeze()  # [seq_len, hidden_size]
    cell_states = torch.stack(model.cell_states).squeeze()
    forget_gates = torch.stack(model.forget_gates).squeeze()

# 可视化分析
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# 1. 训练损失曲线
ax1.plot(losses, color='blue', linewidth=2)
ax1.set_xlabel('Epoch')
ax1.set_ylabel('MSE Loss')
ax1.set_title('Training Loss Curve')
ax1.grid(True, alpha=0.3)

# 2. 遗忘门激活热力图
forget_gates_np = forget_gates.numpy()
sns.heatmap(forget_gates_np.T, cmap='RdYlBu_r', ax=ax2, cbar=True)
ax2.set_xlabel('Time Step')
ax2.set_ylabel('Hidden Unit')
ax2.set_title('Forget Gate Activations')

# 3. 隐藏状态演化
hidden_states_np = hidden_states.numpy()
# 选择几个代表性的隐藏单元
selected_units = [0, 8, 16, 24]
for i, unit in enumerate(selected_units):
    ax3.plot(hidden_states_np[:, unit], label=f'Unit {unit}', linewidth=2)

ax3.set_xlabel('Time Step')
ax3.set_ylabel('Hidden State Value')
ax3.set_title('Hidden State Evolution')
ax3.legend()
ax3.grid(True, alpha=0.3)

# 4. 梯度流分析（通过隐藏状态的变化率近似）
gradient_proxy = np.diff(hidden_states_np, axis=0)
gradient_magnitude = np.linalg.norm(gradient_proxy, axis=1)

ax4.plot(gradient_magnitude, color='red', linewidth=2)
ax4.set_xlabel('Time Step')
ax4.set_ylabel('Gradient Magnitude (Proxy)')
ax4.set_title('Gradient Flow Analysis')
ax4.grid(True, alpha=0.3)

# 添加梯度消失/爆炸的阈值线
ax4.axhline(y=np.mean(gradient_magnitude) + 2*np.std(gradient_magnitude), 
           color='orange', linestyle='--', label='Gradient Explosion Threshold')
ax4.axhline(y=np.mean(gradient_magnitude) - 2*np.std(gradient_magnitude), 
           color='purple', linestyle='--', label='Gradient Vanishing Threshold')
ax4.legend()

plt.tight_layout()
plt.show()

# 分析结果
print(f"\nAnalysis Results:")
print(f"Final training loss: {losses[-1]:.6f}")
print(f"Average forget gate activation: {forget_gates_np.mean():.4f}")
print(f"Forget gate activation std: {forget_gates_np.std():.4f}")

# 分析长期依赖捕获能力
early_hidden = hidden_states_np[:10].mean(axis=0)
late_hidden = hidden_states_np[-10:].mean(axis=0)
correlation = np.corrcoef(early_hidden, late_hidden)[0, 1]
print(f"Early-Late hidden state correlation: {correlation:.4f}")

# 判断梯度流状态
if gradient_magnitude.max() > np.mean(gradient_magnitude) + 2*np.std(gradient_magnitude):
    print("Potential gradient explosion detected")
elif gradient_magnitude.min() < np.mean(gradient_magnitude) - 2*np.std(gradient_magnitude):
    print("Potential gradient vanishing detected")
else:
    print("Gradient flow appears stable")
