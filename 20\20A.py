import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.optimize import minimize
from scipy.signal import find_peaks
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
from mpl_toolkits.mplot3d import Axes3D
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
np.random.seed(101112)

# 定义复杂的生态系统动力学模型 - 捕食者-猎物-竞争者三物种系统
def ecosystem_dynamics(t, y, params):
    """
    三物种生态系统动力学方程
    y = [N1, N2, N3] 分别表示猎物、捕食者1、捕食者2的种群密度
    """
    N1, N2, N3 = y
    
    # 参数解包
    r1, K1, a12, a13, r2, e12, d2, a23, r3, e13, d3, a32 = params
    
    # 猎物种群 (logistic增长 + 被捕食)
    dN1_dt = r1 * N1 * (1 - N1/K1) - a12 * N1 * N2 - a13 * N1 * N3
    
    # 捕食者1 (以猎物为食 + 与捕食者2竞争)
    dN2_dt = e12 * a12 * N1 * N2 - d2 * N2 - a23 * N2 * N3
    
    # 捕食者2 (以猎物为食 + 与捕食者1竞争)
    dN3_dt = e13 * a13 * N1 * N3 - d3 * N3 - a32 * N3 * N2
    
    return [dN1_dt, dN2_dt, dN3_dt]

# 定义不同的生态系统参数组合
ecosystem_scenarios = {
    'Stable Coexistence': {
        'params': [1.5, 100, 0.02, 0.015, 0.8, 0.6, 0.3, 0.01, 0.7, 0.5, 0.25, 0.008],
        'initial': [50, 20, 15],
        'color': 'blue'
    },
    'Competitive Exclusion': {
        'params': [1.2, 80, 0.025, 0.02, 0.9, 0.7, 0.4, 0.02, 0.6, 0.4, 0.35, 0.015],
        'initial': [40, 25, 20],
        'color': 'red'
    },
    'Oscillatory Dynamics': {
        'params': [2.0, 120, 0.03, 0.025, 1.0, 0.8, 0.2, 0.005, 0.9, 0.7, 0.15, 0.003],
        'initial': [60, 15, 10],
        'color': 'green'
    },
    'Predator Dominance': {
        'params': [1.0, 90, 0.015, 0.035, 0.6, 0.9, 0.1, 0.001, 1.2, 0.8, 0.05, 0.001],
        'initial': [45, 10, 30],
        'color': 'orange'
    }
}

# 时间参数
t_span = (0, 50)
t_eval = np.linspace(0, 50, 2000)

# 创建图形
fig = plt.figure(figsize=(16, 12))

# 存储解
solutions = {}
equilibria = {}

# 求解每个场景
for scenario_name, scenario_data in ecosystem_scenarios.items():
    sol = solve_ivp(
        ecosystem_dynamics, 
        t_span, 
        scenario_data['initial'], 
        args=(scenario_data['params'],),
        t_eval=t_eval, 
        method='RK45', 
        rtol=1e-8
    )
    solutions[scenario_name] = sol
    
    # 计算平衡点 (最后时刻的值作为近似)
    equilibria[scenario_name] = sol.y[:, -1]

# 1. 时间序列图
ax1 = plt.subplot(2, 3, 1)
for scenario_name, sol in solutions.items():
    color = ecosystem_scenarios[scenario_name]['color']
    plt.plot(sol.t, sol.y[0], color=color, linewidth=2, label=f'{scenario_name} - Prey', alpha=0.8)
    plt.plot(sol.t, sol.y[1], color=color, linewidth=2, linestyle='--', label=f'{scenario_name} - Pred1', alpha=0.6)
    plt.plot(sol.t, sol.y[2], color=color, linewidth=2, linestyle=':', label=f'{scenario_name} - Pred2', alpha=0.6)

plt.xlabel('Time')
plt.ylabel('Population Density')
plt.title('Population Dynamics Over Time')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=6)
plt.grid(True, alpha=0.3)

# 2. 3D相空间轨道
ax2 = plt.subplot(2, 3, 2, projection='3d')
for scenario_name, sol in solutions.items():
    color = ecosystem_scenarios[scenario_name]['color']
    ax2.plot(sol.y[0], sol.y[1], sol.y[2], color=color, linewidth=2, alpha=0.8, label=scenario_name)
    
    # 标记起始点和终点
    ax2.scatter(sol.y[0][0], sol.y[1][0], sol.y[2][0], color=color, s=100, marker='o', edgecolors='black')
    ax2.scatter(sol.y[0][-1], sol.y[1][-1], sol.y[2][-1], color=color, s=100, marker='s', edgecolors='black')

ax2.set_xlabel('Prey (N1)')
ax2.set_ylabel('Predator 1 (N2)')
ax2.set_zlabel('Predator 2 (N3)')
ax2.set_title('3D Phase Space Trajectories')
ax2.legend(fontsize=8)

# 3. 种群比例饼图 (平衡态)
ax3 = plt.subplot(2, 3, 3)
scenario_names = list(ecosystem_scenarios.keys())
n_scenarios = len(scenario_names)

# 创建子图网格显示饼图
for i, scenario_name in enumerate(scenario_names):
    equilibrium = equilibria[scenario_name]
    total_pop = np.sum(equilibrium)
    
    if total_pop > 0:
        proportions = equilibrium / total_pop
        colors_pie = ['lightblue', 'lightcoral', 'lightgreen']
        
        # 计算子图位置
        row = i // 2
        col = i % 2
        
        # 在主图中创建小饼图
        center_x = 0.2 + col * 0.6
        center_y = 0.7 - row * 0.4
        
        wedges, texts = plt.pie(proportions, colors=colors_pie, center=(center_x, center_y), 
                               radius=0.15, startangle=90)
        
        plt.text(center_x, center_y - 0.25, scenario_name, ha='center', va='center', 
                fontsize=8, fontweight='bold')

plt.xlim(0, 1)
plt.ylim(0, 1)
plt.title('Equilibrium Population Proportions')
plt.axis('off')

# 添加图例
legend_elements = [plt.Rectangle((0,0),1,1, facecolor='lightblue', label='Prey'),
                  plt.Rectangle((0,0),1,1, facecolor='lightcoral', label='Predator 1'),
                  plt.Rectangle((0,0),1,1, facecolor='lightgreen', label='Predator 2')]
plt.legend(handles=legend_elements, loc='upper right')

# 4. 稳定性分析 - 李雅普诺夫指数近似
ax4 = plt.subplot(2, 3, 4)
lyapunov_estimates = []
scenario_labels = []

for scenario_name, sol in solutions.items():
    # 计算轨道的发散性 (简化的李雅普诺夫指数估计)
    # 使用相邻点之间的距离变化率
    trajectory = sol.y.T
    distances = []
    
    for i in range(1, len(trajectory)):
        dist = np.linalg.norm(trajectory[i] - trajectory[i-1])
        distances.append(dist)
    
    # 计算平均发散率
    if len(distances) > 0:
        log_distances = np.log(np.array(distances) + 1e-10)
        lyapunov_est = np.mean(np.diff(log_distances))
        lyapunov_estimates.append(lyapunov_est)
        scenario_labels.append(scenario_name)

colors_bar = [ecosystem_scenarios[name]['color'] for name in scenario_labels]
bars = plt.bar(range(len(lyapunov_estimates)), lyapunov_estimates, color=colors_bar, alpha=0.8)

plt.xlabel('Ecosystem Scenario')
plt.ylabel('Lyapunov Exponent (approx)')
plt.title('System Stability Analysis')
plt.xticks(range(len(scenario_labels)), [name.split()[0] for name in scenario_labels], rotation=45)
plt.grid(True, alpha=0.3)
plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)

# 5. 生物多样性指数分析
ax5 = plt.subplot(2, 3, 5)
diversity_metrics = {}

for scenario_name, sol in solutions.items():
    # 计算Shannon多样性指数随时间的变化
    shannon_indices = []
    
    for i in range(len(sol.t)):
        populations = sol.y[:, i]
        total_pop = np.sum(populations)
        
        if total_pop > 0:
            proportions = populations / total_pop
            # 避免log(0)
            proportions = proportions[proportions > 0]
            shannon_index = -np.sum(proportions * np.log(proportions))
            shannon_indices.append(shannon_index)
        else:
            shannon_indices.append(0)
    
    diversity_metrics[scenario_name] = shannon_indices
    color = ecosystem_scenarios[scenario_name]['color']
    plt.plot(sol.t, shannon_indices, color=color, linewidth=2, label=scenario_name, alpha=0.8)

plt.xlabel('Time')
plt.ylabel('Shannon Diversity Index')
plt.title('Biodiversity Evolution')
plt.legend(fontsize=8)
plt.grid(True, alpha=0.3)

# 6. 参数敏感性热力图
ax6 = plt.subplot(2, 3, 6)
# 选择一个基准场景进行敏感性分析
base_scenario = 'Stable Coexistence'
base_params = ecosystem_scenarios[base_scenario]['params']
base_initial = ecosystem_scenarios[base_scenario]['initial']

# 参数名称
param_names = ['r1', 'K1', 'a12', 'a13', 'r2', 'e12', 'd2', 'a23', 'r3', 'e13', 'd3', 'a32']

# 计算参数敏感性
sensitivity_matrix = np.zeros((len(param_names), 3))  # 3个物种
perturbation = 0.1  # 10%扰动

for i, param_name in enumerate(param_names):
    # 扰动参数
    perturbed_params = base_params.copy()
    perturbed_params[i] *= (1 + perturbation)
    
    # 求解扰动后的系统
    sol_perturbed = solve_ivp(
        ecosystem_dynamics, 
        (0, 20), 
        base_initial, 
        args=(perturbed_params,),
        t_eval=np.linspace(0, 20, 500), 
        method='RK45'
    )
    
    # 计算最终状态的相对变化
    if sol_perturbed.success:
        base_final = equilibria[base_scenario]
        perturbed_final = sol_perturbed.y[:, -1]
        
        for j in range(3):
            if base_final[j] > 0:
                sensitivity_matrix[i, j] = abs((perturbed_final[j] - base_final[j]) / base_final[j])

# 绘制热力图
im = plt.imshow(sensitivity_matrix, cmap='Reds', aspect='auto')
plt.colorbar(im, label='Relative Sensitivity')
plt.xticks(range(3), ['Prey', 'Predator 1', 'Predator 2'])
plt.yticks(range(len(param_names)), param_names)
plt.title('Parameter Sensitivity Analysis')

# 添加数值标注
for i in range(len(param_names)):
    for j in range(3):
        plt.text(j, i, f'{sensitivity_matrix[i,j]:.3f}', 
                ha='center', va='center', fontsize=8)

plt.tight_layout()
plt.suptitle('Three-Species Ecosystem Dynamics: Predator-Prey-Competition Model\n' +
             'Circles: Initial states, Squares: Final states', 
             fontsize=14, y=0.98)

plt.show()

# 打印详细分析结果
print("=== 三物种生态系统动力学分析结果 ===")
for scenario_name, sol in solutions.items():
    print(f"\n{scenario_name}:")
    equilibrium = equilibria[scenario_name]
    print(f"  平衡态种群密度: 猎物={equilibrium[0]:.2f}, 捕食者1={equilibrium[1]:.2f}, 捕食者2={equilibrium[2]:.2f}")
    
    # 计算最终多样性指数
    total_pop = np.sum(equilibrium)
    if total_pop > 0:
        proportions = equilibrium / total_pop
        proportions = proportions[proportions > 0]
        final_diversity = -np.sum(proportions * np.log(proportions))
        print(f"  最终多样性指数: {final_diversity:.3f}")
    
    # 判断系统稳定性
    final_populations = sol.y[:, -500:]  # 最后500个时间点
    stability = np.std(final_populations, axis=1) / np.mean(final_populations, axis=1)
    print(f"  种群稳定性 (变异系数): 猎物={stability[0]:.3f}, 捕食者1={stability[1]:.3f}, 捕食者2={stability[2]:.3f}")
    
    if np.all(stability < 0.1):
        print("  系统状态: 稳定平衡")
    elif np.any(stability > 0.5):
        print("  系统状态: 强振荡或混沌")
    else:
        print("  系统状态: 弱振荡")
