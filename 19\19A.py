import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import differential_evolution, minimize
from scipy.spatial.distance import pdist, squareform
import matplotlib.patches as patches
from mpl_toolkits.mplot3d import Axes3D
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
np.random.seed(789)

# 定义复杂的多目标优化问题 - 投资组合优化
class PortfolioOptimizer:
    def __init__(self, n_assets=6, n_scenarios=1000):
        self.n_assets = n_assets
        self.n_scenarios = n_scenarios
        
        # 生成模拟的资产收益数据
        np.random.seed(42)
        
        # 资产名称
        self.asset_names = ['Tech', 'Finance', 'Energy', 'Healthcare', 'Consumer', 'Utilities']
        
        # 生成相关的收益率数据
        # 创建相关性矩阵
        correlation_matrix = np.array([
            [1.00, 0.60, 0.30, 0.40, 0.50, 0.20],
            [0.60, 1.00, 0.25, 0.35, 0.45, 0.30],
            [0.30, 0.25, 1.00, 0.20, 0.30, 0.40],
            [0.40, 0.35, 0.20, 1.00, 0.55, 0.25],
            [0.50, 0.45, 0.30, 0.55, 1.00, 0.35],
            [0.20, 0.30, 0.40, 0.25, 0.35, 1.00]
        ])
        
        # 期望收益率和波动率
        self.expected_returns = np.array([0.12, 0.10, 0.08, 0.11, 0.09, 0.06])
        self.volatilities = np.array([0.20, 0.15, 0.25, 0.18, 0.16, 0.10])
        
        # 生成协方差矩阵
        self.cov_matrix = np.outer(self.volatilities, self.volatilities) * correlation_matrix
        
        # 生成蒙特卡洛情景
        self.scenarios = np.random.multivariate_normal(
            self.expected_returns, self.cov_matrix, self.n_scenarios
        )
    
    def portfolio_metrics(self, weights):
        """计算投资组合的风险收益指标"""
        weights = np.array(weights)
        
        # 期望收益
        expected_return = np.dot(weights, self.expected_returns)
        
        # 投资组合方差
        portfolio_variance = np.dot(weights, np.dot(self.cov_matrix, weights))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # 夏普比率 (假设无风险利率为3%)
        risk_free_rate = 0.03
        sharpe_ratio = (expected_return - risk_free_rate) / portfolio_volatility
        
        # VaR (Value at Risk) - 95%置信度
        portfolio_returns = np.dot(self.scenarios, weights)
        var_95 = np.percentile(portfolio_returns, 5)
        
        # CVaR (Conditional Value at Risk)
        cvar_95 = np.mean(portfolio_returns[portfolio_returns <= var_95])
        
        return {
            'return': expected_return,
            'volatility': portfolio_volatility,
            'sharpe': sharpe_ratio,
            'var_95': var_95,
            'cvar_95': cvar_95
        }
    
    def objective_function(self, weights, objective_type='sharpe'):
        """多目标优化的目标函数"""
        metrics = self.portfolio_metrics(weights)
        
        if objective_type == 'sharpe':
            return -metrics['sharpe']  # 最大化夏普比率
        elif objective_type == 'return':
            return -metrics['return']  # 最大化收益
        elif objective_type == 'risk':
            return metrics['volatility']  # 最小化风险
        elif objective_type == 'cvar':
            return -metrics['cvar_95']  # 最小化CVaR (最大化负CVaR)

# 创建优化器实例
optimizer = PortfolioOptimizer()

# 约束条件：权重和为1，每个权重在0-0.5之间
bounds = [(0, 0.5) for _ in range(optimizer.n_assets)]
constraint = {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}

# 不同的优化目标
objectives = ['sharpe', 'return', 'risk', 'cvar']
objective_names = ['Max Sharpe Ratio', 'Max Return', 'Min Risk', 'Min CVaR']

# 存储优化结果
optimal_portfolios = {}
efficient_frontier_data = []

# 创建图形
fig = plt.figure(figsize=(16, 12))

# 1. 优化不同目标
for obj_type, obj_name in zip(objectives, objective_names):
    result = minimize(
        lambda x: optimizer.objective_function(x, obj_type),
        x0=np.ones(optimizer.n_assets) / optimizer.n_assets,
        method='SLSQP',
        bounds=bounds,
        constraints=constraint,
        options={'maxiter': 1000}
    )
    
    optimal_portfolios[obj_type] = {
        'weights': result.x,
        'metrics': optimizer.portfolio_metrics(result.x)
    }

# 2. 生成有效前沿
target_returns = np.linspace(0.06, 0.12, 20)
efficient_portfolios = []

for target_return in target_returns:
    # 添加收益约束
    return_constraint = {'type': 'eq', 'fun': lambda x: np.dot(x, optimizer.expected_returns) - target_return}
    constraints = [constraint, return_constraint]
    
    result = minimize(
        lambda x: optimizer.objective_function(x, 'risk'),
        x0=np.ones(optimizer.n_assets) / optimizer.n_assets,
        method='SLSQP',
        bounds=bounds,
        constraints=constraints,
        options={'maxiter': 1000}
    )
    
    if result.success:
        metrics = optimizer.portfolio_metrics(result.x)
        efficient_portfolios.append({
            'weights': result.x,
            'return': metrics['return'],
            'volatility': metrics['volatility'],
            'sharpe': metrics['sharpe']
        })

# 绘制有效前沿
ax1 = plt.subplot(2, 3, 1)
if efficient_portfolios:
    returns = [p['return'] for p in efficient_portfolios]
    volatilities = [p['volatility'] for p in efficient_portfolios]
    sharpe_ratios = [p['sharpe'] for p in efficient_portfolios]
    
    # 用夏普比率着色
    scatter = plt.scatter(volatilities, returns, c=sharpe_ratios, cmap='viridis', s=50, alpha=0.8)
    plt.colorbar(scatter, label='Sharpe Ratio')
    
    # 标记特殊点
    colors = ['red', 'blue', 'green', 'orange']
    markers = ['o', 's', '^', 'D']
    
    for i, (obj_type, obj_name) in enumerate(zip(objectives, objective_names)):
        portfolio = optimal_portfolios[obj_type]
        plt.scatter(portfolio['metrics']['volatility'], portfolio['metrics']['return'],
                   c=colors[i], marker=markers[i], s=100, edgecolors='black', 
                   linewidth=2, label=obj_name, zorder=10)

plt.xlabel('Volatility (Risk)')
plt.ylabel('Expected Return')
plt.title('Efficient Frontier')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
plt.grid(True, alpha=0.3)

# 绘制资产权重分布
ax2 = plt.subplot(2, 3, 2)
width = 0.2
x = np.arange(len(optimizer.asset_names))

for i, (obj_type, obj_name) in enumerate(zip(objectives, objective_names)):
    weights = optimal_portfolios[obj_type]['weights']
    plt.bar(x + i*width, weights, width, label=obj_name, color=colors[i], alpha=0.8)

plt.xlabel('Assets')
plt.ylabel('Weight')
plt.title('Optimal Portfolio Weights')
plt.xticks(x + width*1.5, optimizer.asset_names, rotation=45)
plt.legend(fontsize=8)
plt.grid(True, alpha=0.3)

# 绘制风险收益指标对比
ax3 = plt.subplot(2, 3, 3)
metrics_names = ['Return', 'Volatility', 'Sharpe', 'VaR_95', 'CVaR_95']
metrics_data = []

for obj_type in objectives:
    portfolio = optimal_portfolios[obj_type]
    metrics = portfolio['metrics']
    metrics_data.append([
        metrics['return'],
        metrics['volatility'],
        metrics['sharpe'],
        abs(metrics['var_95']),  # 取绝对值便于比较
        abs(metrics['cvar_95'])
    ])

metrics_data = np.array(metrics_data).T
x_pos = np.arange(len(objectives))

for i, metric_name in enumerate(metrics_names):
    plt.plot(x_pos, metrics_data[i], marker='o', linewidth=2, label=metric_name)

plt.xlabel('Optimization Objective')
plt.ylabel('Metric Value')
plt.title('Risk-Return Metrics Comparison')
plt.xticks(x_pos, [name.split()[1] for name in objective_names], rotation=45)
plt.legend(fontsize=8)
plt.grid(True, alpha=0.3)

# 绘制相关性热力图
ax4 = plt.subplot(2, 3, 4)
im = plt.imshow(optimizer.cov_matrix, cmap='RdYlBu_r', aspect='auto')
plt.colorbar(im, label='Covariance')
plt.xticks(range(len(optimizer.asset_names)), optimizer.asset_names, rotation=45)
plt.yticks(range(len(optimizer.asset_names)), optimizer.asset_names)
plt.title('Asset Covariance Matrix')

# 添加数值标注
for i in range(len(optimizer.asset_names)):
    for j in range(len(optimizer.asset_names)):
        plt.text(j, i, f'{optimizer.cov_matrix[i,j]:.3f}', 
                ha='center', va='center', fontsize=8)

# 绘制蒙特卡洛模拟结果
ax5 = plt.subplot(2, 3, 5)
# 选择夏普比率最优组合进行分析
optimal_weights = optimal_portfolios['sharpe']['weights']
portfolio_scenarios = np.dot(optimizer.scenarios, optimal_weights)

plt.hist(portfolio_scenarios, bins=50, alpha=0.7, density=True, color='skyblue', edgecolor='black')
plt.axvline(np.mean(portfolio_scenarios), color='red', linestyle='--', linewidth=2, label='Mean')
plt.axvline(np.percentile(portfolio_scenarios, 5), color='orange', linestyle='--', linewidth=2, label='VaR 95%')
plt.axvline(np.percentile(portfolio_scenarios, 1), color='darkred', linestyle='--', linewidth=2, label='VaR 99%')

plt.xlabel('Portfolio Return')
plt.ylabel('Probability Density')
plt.title('Portfolio Return Distribution (Max Sharpe)')
plt.legend()
plt.grid(True, alpha=0.3)

# 绘制资产收益相关性网络
ax6 = plt.subplot(2, 3, 6)
# 计算相关系数矩阵
corr_matrix = np.corrcoef(optimizer.scenarios.T)

# 绘制网络图
theta = np.linspace(0, 2*np.pi, optimizer.n_assets, endpoint=False)
x = np.cos(theta)
y = np.sin(theta)

# 绘制节点
for i, (xi, yi, name) in enumerate(zip(x, y, optimizer.asset_names)):
    plt.scatter(xi, yi, s=200, c=colors[i % len(colors)], alpha=0.8, edgecolors='black', linewidth=2)
    plt.text(xi*1.2, yi*1.2, name, ha='center', va='center', fontweight='bold')

# 绘制连接线 (只显示强相关性)
for i in range(optimizer.n_assets):
    for j in range(i+1, optimizer.n_assets):
        corr = corr_matrix[i, j]
        if abs(corr) > 0.3:  # 只显示相关性大于0.3的连接
            alpha = abs(corr)
            linewidth = abs(corr) * 3
            color = 'red' if corr > 0 else 'blue'
            plt.plot([x[i], x[j]], [y[i], y[j]], color=color, alpha=alpha, linewidth=linewidth)

plt.xlim(-1.5, 1.5)
plt.ylim(-1.5, 1.5)
plt.title('Asset Correlation Network\n(Red: Positive, Blue: Negative)')
plt.axis('equal')
plt.axis('off')

plt.tight_layout()
plt.suptitle('Multi-Objective Portfolio Optimization Analysis\n' +
             'Efficient Frontier, Risk Metrics, and Asset Correlations', 
             fontsize=14, y=0.98)

plt.show()

# 打印详细结果
print("=== 多目标投资组合优化结果 ===")
for obj_type, obj_name in zip(objectives, objective_names):
    portfolio = optimal_portfolios[obj_type]
    print(f"\n{obj_name}:")
    print("  资产权重:")
    for i, (name, weight) in enumerate(zip(optimizer.asset_names, portfolio['weights'])):
        print(f"    {name}: {weight:.3f}")
    
    metrics = portfolio['metrics']
    print(f"  期望收益: {metrics['return']:.4f}")
    print(f"  波动率: {metrics['volatility']:.4f}")
    print(f"  夏普比率: {metrics['sharpe']:.4f}")
    print(f"  VaR (95%): {metrics['var_95']:.4f}")
    print(f"  CVaR (95%): {metrics['cvar_95']:.4f}")
