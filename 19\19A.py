# 题目19：强化学习Q-learning收敛性分析
# 图片内容：显示不同学习率下Q值函数的收敛过程和策略性能

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict

class GridWorld:
    def __init__(self, size=5):
        self.size = size
        self.start = (0, 0)
        self.goal = (size-1, size-1)
        self.obstacles = [(1, 1), (2, 2), (3, 1), (1, 3)]
        self.actions = ['up', 'down', 'left', 'right']
        self.action_effects = {
            'up': (-1, 0),
            'down': (1, 0),
            'left': (0, -1),
            'right': (0, 1)
        }
    
    def is_valid_state(self, state):
        row, col = state
        return (0 <= row < self.size and 0 <= col < self.size and 
                state not in self.obstacles)
    
    def get_next_state(self, state, action):
        if state == self.goal:
            return state
        
        dr, dc = self.action_effects[action]
        next_state = (state[0] + dr, state[1] + dc)
        
        if self.is_valid_state(next_state):
            return next_state
        else:
            return state  # 撞墙或障碍物，保持原位
    
    def get_reward(self, state, action, next_state):
        if next_state == self.goal:
            return 10.0
        elif next_state in self.obstacles:
            return -5.0
        elif next_state == state:  # 撞墙
            return -1.0
        else:
            return -0.1  # 每步小惩罚

class QLearningAgent:
    def __init__(self, env, learning_rate=0.1, discount_factor=0.9, epsilon=0.1):
        self.env = env
        self.lr = learning_rate
        self.gamma = discount_factor
        self.epsilon = epsilon
        self.q_table = defaultdict(lambda: defaultdict(float))
        self.q_history = []
    
    def get_action(self, state):
        if np.random.random() < self.epsilon:
            return np.random.choice(self.env.actions)
        else:
            q_values = [self.q_table[state][action] for action in self.env.actions]
            max_q = max(q_values)
            best_actions = [action for action, q in zip(self.env.actions, q_values) if q == max_q]
            return np.random.choice(best_actions)
    
    def update_q_value(self, state, action, reward, next_state):
        current_q = self.q_table[state][action]
        next_max_q = max([self.q_table[next_state][a] for a in self.env.actions])
        new_q = current_q + self.lr * (reward + self.gamma * next_max_q - current_q)
        self.q_table[state][action] = new_q
    
    def train_episode(self):
        state = self.env.start
        total_reward = 0
        steps = 0
        
        while state != self.env.goal and steps < 100:
            action = self.get_action(state)
            next_state = self.env.get_next_state(state, action)
            reward = self.env.get_reward(state, action, next_state)
            
            self.update_q_value(state, action, reward, next_state)
            
            state = next_state
            total_reward += reward
            steps += 1
        
        # 记录Q值变化
        q_sum = sum(sum(actions.values()) for actions in self.q_table.values())
        self.q_history.append(q_sum)
        
        return total_reward, steps

# 实验设置
env = GridWorld(size=5)
learning_rates = [0.01, 0.1, 0.3, 0.9]
num_episodes = 500
num_runs = 5

# 存储结果
results = {}

for lr in learning_rates:
    print(f"Training with learning rate: {lr}")
    
    all_rewards = []
    all_q_histories = []
    
    for run in range(num_runs):
        agent = QLearningAgent(env, learning_rate=lr)
        episode_rewards = []
        
        for episode in range(num_episodes):
            reward, steps = agent.train_episode()
            episode_rewards.append(reward)
        
        all_rewards.append(episode_rewards)
        all_q_histories.append(agent.q_history)
    
    # 计算平均值
    avg_rewards = np.mean(all_rewards, axis=0)
    avg_q_history = np.mean(all_q_histories, axis=0)
    
    results[lr] = {
        'rewards': avg_rewards,
        'q_history': avg_q_history,
        'final_q_table': agent.q_table
    }

# 可视化结果
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

# 1. 学习曲线（累积奖励）
for lr in learning_rates:
    # 计算滑动平均
    window_size = 20
    rewards = results[lr]['rewards']
    smoothed_rewards = np.convolve(rewards, np.ones(window_size)/window_size, mode='valid')
    episodes_smooth = range(window_size-1, num_episodes)
    
    ax1.plot(episodes_smooth, smoothed_rewards, label=f'LR={lr}', linewidth=2)

ax1.set_xlabel('Episode')
ax1.set_ylabel('Average Reward (Smoothed)')
ax1.set_title('Learning Curves for Different Learning Rates')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 2. Q值收敛性
for lr in learning_rates:
    q_history = results[lr]['q_history']
    ax2.plot(range(len(q_history)), q_history, label=f'LR={lr}', linewidth=2)

ax2.set_xlabel('Episode')
ax2.set_ylabel('Sum of Q-values')
ax2.set_title('Q-value Convergence')
ax2.legend()
ax2.grid(True, alpha=0.3)

# 3. 最终策略可视化（以LR=0.1为例）
best_lr = 0.1
q_table = results[best_lr]['final_q_table']

# 创建策略矩阵
policy_matrix = np.full((env.size, env.size), '', dtype=object)
value_matrix = np.zeros((env.size, env.size))

for i in range(env.size):
    for j in range(env.size):
        state = (i, j)
        if state == env.goal:
            policy_matrix[i, j] = 'G'
            value_matrix[i, j] = 0
        elif state in env.obstacles:
            policy_matrix[i, j] = 'X'
            value_matrix[i, j] = -10
        else:
            q_values = [q_table[state][action] for action in env.actions]
            best_action_idx = np.argmax(q_values)
            best_action = env.actions[best_action_idx]
            
            action_symbols = {'up': '↑', 'down': '↓', 'left': '←', 'right': '→'}
            policy_matrix[i, j] = action_symbols[best_action]
            value_matrix[i, j] = max(q_values)

# 绘制策略热力图
sns.heatmap(value_matrix, annot=policy_matrix, fmt='', cmap='RdYlBu_r', 
            ax=ax3, cbar=True, square=True)
ax3.set_title(f'Optimal Policy (LR={best_lr})')
ax3.set_xlabel('Column')
ax3.set_ylabel('Row')

# 4. 学习率对收敛速度的影响
convergence_episodes = []
for lr in learning_rates:
    rewards = results[lr]['rewards']
    # 找到达到90%最终性能的回合数
    final_performance = np.mean(rewards[-50:])
    target_performance = 0.9 * final_performance
    
    convergence_episode = num_episodes
    for i, reward in enumerate(rewards):
        if reward >= target_performance:
            convergence_episode = i
            break
    
    convergence_episodes.append(convergence_episode)

ax4.bar(range(len(learning_rates)), convergence_episodes, 
        tick_label=[f'LR={lr}' for lr in learning_rates],
        color=['skyblue', 'lightgreen', 'orange', 'pink'])
ax4.set_ylabel('Episodes to Convergence')
ax4.set_title('Convergence Speed vs Learning Rate')
ax4.grid(True, alpha=0.3, axis='y')

plt.tight_layout()
plt.show()

# 输出统计信息
print("\nFinal Performance Summary:")
for lr in learning_rates:
    final_reward = np.mean(results[lr]['rewards'][-50:])
    print(f"Learning Rate {lr}: Final Average Reward = {final_reward:.2f}")

# 分析最优学习率
best_performance_lr = max(learning_rates, 
                         key=lambda lr: np.mean(results[lr]['rewards'][-50:]))
print(f"\nBest performing learning rate: {best_performance_lr}")
